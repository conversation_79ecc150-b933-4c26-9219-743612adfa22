#!/usr/bin/env python3
"""
医学影像数据集测试脚本

测试 MedicalImageDataset 类的各种功能，包括：
- 数据加载
- 切片提取
- 标签解析
- 数据变换
- 错误处理
"""

import os
import sys
import unittest
import tempfile
import shutil
import numpy as np
import SimpleITK as sitk
import torch
from PIL import Image

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_loader import MedicalImageDataset, get_medical_data_loaders
from src.utils import get_nii_info, extract_nii_slices
from config.config import Config
from torchvision import transforms


class TestMedicalDataset(unittest.TestCase):
    """医学影像数据集测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        self.config = Config()
        
        # 创建测试用的nii.gz文件
        self.create_test_nii_files()
        
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.test_dir)
    
    def create_test_nii_files(self):
        """创建测试用的nii.gz文件"""
        # 创建目录结构
        sequences = ['t1', 't2', 'flair', 'dwi']
        splits = ['train', 'val']
        
        for split in splits:
            for seq in sequences:
                seq_dir = os.path.join(self.test_dir, split, seq)
                os.makedirs(seq_dir, exist_ok=True)
                
                # 创建2个测试文件
                for i in range(2):
                    # 创建随机3D数组
                    arr = np.random.randint(0, 1000, (64, 64, 64), dtype=np.int16)
                    
                    # 创建SimpleITK图像
                    image = sitk.GetImageFromArray(arr)
                    image.SetSpacing([1.0, 1.0, 1.0])
                    image.SetOrigin([0.0, 0.0, 0.0])
                    
                    # 保存为nii.gz文件
                    filename = f"test_{seq}_{i:03d}.nii.gz"
                    filepath = os.path.join(seq_dir, filename)
                    sitk.WriteImage(image, filepath)
    
    def test_dataset_creation_directory_structure(self):
        """测试基于目录结构的数据集创建"""
        dataset = MedicalImageDataset(
            data_dir=os.path.join(self.test_dir, 'train'),
            slice_mode='middle'
        )
        
        # 检查数据集大小
        self.assertEqual(len(dataset), 8)  # 4个序列 × 2个文件
        
        # 检查类别名称
        class_names = dataset.get_class_names()
        expected_classes = ['dwi', 'flair', 't1', 't2']  # 按字母顺序排序
        self.assertEqual(sorted(class_names), expected_classes)
        
        # 检查标签映射
        self.assertEqual(len(dataset.label_to_idx), 4)
        
    def test_middle_slice_mode(self):
        """测试middle切片模式"""
        dataset = MedicalImageDataset(
            data_dir=os.path.join(self.test_dir, 'train'),
            slice_mode='middle'
        )
        
        # 获取一个样本
        image, label = dataset[0]
        
        # 检查图像类型
        self.assertIsInstance(image, Image.Image)
        self.assertIsInstance(label, int)
        
        # 检查标签范围
        self.assertGreaterEqual(label, 0)
        self.assertLess(label, 4)
    
    def test_multi_slice_mode(self):
        """测试multi切片模式"""
        for axis in ['x', 'y', 'z']:
            with self.subTest(axis=axis):
                dataset = MedicalImageDataset(
                    data_dir=os.path.join(self.test_dir, 'train'),
                    slice_mode='multi',
                    axis=axis
                )
                
                # 获取一个样本
                image, label = dataset[0]
                
                # 检查图像类型
                self.assertIsInstance(image, Image.Image)
                self.assertIsInstance(label, int)
    
    def test_data_transforms(self):
        """测试数据变换"""
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor()
        ])
        
        dataset = MedicalImageDataset(
            data_dir=os.path.join(self.test_dir, 'train'),
            slice_mode='middle',
            transform=transform
        )
        
        # 获取一个样本
        image, _ = dataset[0]

        # 检查变换后的图像
        self.assertEqual(image.shape, (1, 224, 224))  # 灰度图像
        self.assertEqual(image.dtype, torch.float32)
    
    def test_data_loaders_creation(self):
        """测试数据加载器创建"""
        # 临时修改配置
        original_data_dir = self.config.DATA_DIR
        self.config.DATA_DIR = self.test_dir
        
        try:
            data_loaders, class_names, dataset_sizes = get_medical_data_loaders(
                self.config,
                slice_mode='middle'
            )
            
            # 检查数据加载器
            self.assertIn('train', data_loaders)
            self.assertIn('val', data_loaders)
            
            # 检查类别名称
            self.assertEqual(len(class_names), 4)
            
            # 检查数据集大小
            self.assertEqual(dataset_sizes['train'], 8)
            self.assertEqual(dataset_sizes['val'], 8)
            
        finally:
            # 恢复原始配置
            self.config.DATA_DIR = original_data_dir
    
    def test_nii_info_extraction(self):
        """测试nii文件信息提取"""
        # 获取第一个测试文件
        test_file = None
        for root, _, files in os.walk(self.test_dir):
            for file in files:
                if file.endswith('.nii.gz'):
                    test_file = os.path.join(root, file)
                    break
            if test_file:
                break
        
        self.assertIsNotNone(test_file)
        
        # 提取文件信息
        info = get_nii_info(test_file)
        
        # 检查信息完整性
        self.assertIn('shape', info)
        self.assertIn('spacing', info)
        self.assertIn('min_value', info)
        self.assertIn('max_value', info)
        self.assertEqual(info['shape'], (64, 64, 64))
    
    def test_slice_extraction_utility(self):
        """测试切片提取工具函数"""
        # 获取第一个测试文件
        test_file = None
        for root, _, files in os.walk(self.test_dir):
            for file in files:
                if file.endswith('.nii.gz'):
                    test_file = os.path.join(root, file)
                    break
            if test_file:
                break
        
        self.assertIsNotNone(test_file)
        
        # 测试middle模式
        slices_middle = extract_nii_slices(test_file, slice_mode='middle')
        self.assertEqual(len(slices_middle), 3)  # xyz三个面
        
        # 测试multi模式
        for axis in ['x', 'y', 'z']:
            slices_multi = extract_nii_slices(test_file, slice_mode='multi', axis=axis)
            self.assertEqual(len(slices_multi), 3)  # 25%, 50%, 75%三个位置
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试不存在的目录
        dataset = MedicalImageDataset(
            data_dir="/nonexistent/path",
            slice_mode='middle'
        )
        self.assertEqual(len(dataset), 0)
        
        # 测试无效的切片模式
        with self.assertRaises(ValueError):
            dataset = MedicalImageDataset(
                data_dir=self.test_dir,
                slice_mode='invalid_mode'
            )
            dataset[0]  # 触发切片提取
        
        # 测试无效的轴向
        with self.assertRaises(ValueError):
            dataset = MedicalImageDataset(
                data_dir=self.test_dir,
                slice_mode='multi',
                axis='invalid_axis'
            )
            dataset[0]  # 触发切片提取
    
    def test_empty_dataset(self):
        """测试空数据集处理"""
        empty_dir = tempfile.mkdtemp()
        try:
            dataset = MedicalImageDataset(
                data_dir=empty_dir,
                slice_mode='middle'
            )
            self.assertEqual(len(dataset), 0)
            self.assertEqual(len(dataset.get_class_names()), 0)
        finally:
            shutil.rmtree(empty_dir)


def run_performance_test():
    """运行性能测试"""
    print("\n" + "="*60)
    print("性能测试")
    print("="*60)
    
    import time
    
    # 创建临时测试数据
    test_dir = tempfile.mkdtemp()
    try:
        # 创建较大的测试文件
        print("创建测试数据...")
        arr = np.random.randint(0, 1000, (128, 128, 128), dtype=np.int16)
        image = sitk.GetImageFromArray(arr)
        test_file = os.path.join(test_dir, "test.nii.gz")
        sitk.WriteImage(image, test_file)
        
        # 测试不同切片模式的性能
        modes = [('middle', 'z'), ('multi', 'z'), ('multi', 'y'), ('multi', 'x')]
        
        for slice_mode, axis in modes:
            start_time = time.time()
            
            # 提取切片100次
            for _ in range(100):
                _ = extract_nii_slices(test_file, slice_mode=slice_mode, axis=axis)
            
            end_time = time.time()
            avg_time = (end_time - start_time) / 100
            
            print(f"{slice_mode}模式 {axis}轴: 平均 {avg_time:.4f}秒/次")
    
    finally:
        shutil.rmtree(test_dir)


if __name__ == "__main__":
    # 运行单元测试
    print("运行医学影像数据集单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()
