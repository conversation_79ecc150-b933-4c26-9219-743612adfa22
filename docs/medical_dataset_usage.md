# 医学影像数据集使用指南

本文档介绍如何使用新实现的 `MedicalImageDataset` 类来加载和处理 nii.gz 格式的医学影像数据，用于MR序列分类任务。

## 功能特性

- 支持 nii.gz 和 nii 格式的医学影像文件
- 使用 SimpleITK 读取医学影像数据
- 支持两种切片提取模式：
  - **middle模式**: 提取 xyz 面的中间切片
  - **multi模式**: 提取指定轴的 25%、50%、75% 位置切片
- 自动标签解析（支持目录结构和文件名解析）
- 标准化图像预处理
- 与 PyTorch DataLoader 完全兼容

## 数据目录结构

### 方式1：按目录分类
```
data/medical/
├── train/
│   ├── t1/
│   │   ├── patient001_t1.nii.gz
│   │   ├── patient002_t1.nii.gz
│   │   └── ...
│   ├── t2/
│   │   ├── patient001_t2.nii.gz
│   │   ├── patient002_t2.nii.gz
│   │   └── ...
│   ├── flair/
│   │   └── ...
│   └── dwi/
│       └── ...
├── val/
│   └── (同样的结构)
└── test/
    └── (同样的结构)
```

### 方式2：文件名包含标签
```
data/medical/
├── train/
│   ├── patient001_t1.nii.gz
│   ├── patient001_t2.nii.gz
│   ├── patient002_flair.nii.gz
│   ├── patient002_dwi.nii.gz
│   └── ...
├── val/
│   └── ...
└── test/
    └── ...
```

## 基本使用方法

### 1. 导入必要的模块

```python
from src.data_loader import MedicalImageDataset, get_medical_data_loaders
from config.config import Config
from torchvision import transforms
```

### 2. 创建数据集实例

```python
# 配置数据变换
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                        std=[0.229, 0.224, 0.225])
])

# 创建数据集 - middle模式
dataset = MedicalImageDataset(
    data_dir="data/medical/train",
    transform=transform,
    slice_mode='middle',  # 提取xyz面中间切片
    axis='z'  # 在middle模式下此参数无效
)

# 创建数据集 - multi模式
dataset_multi = MedicalImageDataset(
    data_dir="data/medical/train",
    transform=transform,
    slice_mode='multi',   # 提取多个切片
    axis='z'             # 在z轴上提取25%、50%、75%位置
)
```

### 3. 使用便捷函数创建数据加载器

```python
config = Config()

# 创建医学影像数据加载器
data_loaders, class_names, dataset_sizes = get_medical_data_loaders(
    config, 
    slice_mode='middle',  # 或 'multi'
    axis='z'             # 当slice_mode='multi'时有效
)

# 获取训练数据加载器
train_loader = data_loaders['train']
val_loader = data_loaders['val']
```

## 切片模式详解

### Middle模式
提取三个正交面的中间切片：
- **轴向面 (Axial)**: Z轴中间位置的切片
- **冠状面 (Coronal)**: Y轴中间位置的切片  
- **矢状面 (Sagittal)**: X轴中间位置的切片

```python
dataset = MedicalImageDataset(
    data_dir="path/to/data",
    slice_mode='middle'
)
```

### Multi模式
在指定轴向上提取多个位置的切片：
- 25% 位置切片
- 50% 位置切片（中间）
- 75% 位置切片

```python
# Z轴多切片
dataset_z = MedicalImageDataset(
    data_dir="path/to/data",
    slice_mode='multi',
    axis='z'
)

# Y轴多切片
dataset_y = MedicalImageDataset(
    data_dir="path/to/data",
    slice_mode='multi',
    axis='y'
)

# X轴多切片
dataset_x = MedicalImageDataset(
    data_dir="path/to/data",
    slice_mode='multi',
    axis='x'
)
```

## 训练示例

### 使用专门的训练脚本

```bash
# 运行医学影像训练脚本
python src/train_medical.py
```

### 自定义训练循环

```python
from src.train_medical import train_medical_model
from config.config import Config

config = Config()

# 训练模型
model = train_medical_model(
    config,
    slice_mode='middle',  # 或 'multi'
    axis='z',            # 当slice_mode='multi'时指定轴向
    model_name='resnet50' # 或 'resnet18'
)
```

## 数据可视化

使用示例脚本可视化数据集：

```bash
# 运行可视化示例
python examples/medical_dataset_example.py
```

这将创建示例数据目录结构并测试数据加载功能。

## 配置参数

在 `config/config.py` 中可以调整以下参数：

```python
# 医学影像特定参数
SLICE_MODE = 'middle'  # 'middle' 或 'multi'
SLICE_AXIS = 'z'       # 当SLICE_MODE='multi'时使用
NUM_CLASSES = 4        # MR序列类别数量
MR_SEQUENCES = ['t1', 't2', 'flair', 'dwi']  # 序列类型

# 数据路径
MEDICAL_DATA_DIR = "data/medical"

# 训练参数
BATCH_SIZE = 32
IMAGE_SIZE = 224
NUM_EPOCHS = 25
LEARNING_RATE = 1e-4
```

## 注意事项

1. **数据格式**: 确保医学影像文件为 nii.gz 或 nii 格式
2. **内存使用**: 大型医学影像文件可能占用较多内存，建议适当调整批次大小
3. **标签命名**: 确保标签名称一致，支持的序列类型包括 t1、t2、flair、dwi 等
4. **数据预处理**: 图像会自动标准化到 0-255 范围，并应用指定的变换
5. **错误处理**: 如果文件读取失败，会返回默认的黑色图像并打印错误信息

## 故障排除

### 常见问题

1. **ImportError: No module named 'SimpleITK'**
   ```bash
   pip install SimpleITK
   ```

2. **数据集为空**
   - 检查数据目录路径是否正确
   - 确认文件扩展名为 .nii.gz 或 .nii
   - 验证目录结构是否符合要求

3. **内存不足**
   - 减少批次大小 (BATCH_SIZE)
   - 减少工作进程数 (NUM_WORKERS)

4. **标签解析错误**
   - 检查目录名称或文件名格式
   - 确保标签名称不包含特殊字符

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多医学影像格式 (DICOM等)
- 添加更多数据增强方法
- 实现多切片融合策略
- 支持3D卷积网络
- 添加影像质量评估
