#!/usr/bin/env python3
"""
测试优化后的医学影像数据加载器

验证移除PadToSquare后的功能是否正常工作
"""

import os
import sys
import tempfile
import shutil
import numpy as np
import SimpleITK as sitk
import torch
from PIL import Image

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_loader import MedicalImageDataset, get_medical_data_loaders
from config.config import Config


def create_test_nii_file(output_path, shape=(64, 64, 64)):
    """创建测试用的nii.gz文件"""
    # 创建随机3D数组
    arr = np.random.randint(0, 1000, shape, dtype=np.int16)
    
    # 创建SimpleITK图像
    image = sitk.GetImageFromArray(arr)
    image.SetSpacing([1.0, 1.0, 1.0])
    image.SetOrigin([0.0, 0.0, 0.0])
    
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存文件
    sitk.WriteImage(image, output_path)
    return output_path


def test_dataset_output_format():
    """测试数据集输出格式"""
    print("测试数据集输出格式...")
    
    # 创建临时测试数据
    test_dir = tempfile.mkdtemp()
    try:
        # 创建测试文件
        test_file = os.path.join(test_dir, "train", "t1", "test.nii.gz")
        create_test_nii_file(test_file)
        
        # 创建数据集
        dataset = MedicalImageDataset(
            data_dir=os.path.join(test_dir, "train"),
            slice_mode='middle'
        )
        
        if len(dataset) > 0:
            # 获取样本
            image, label = dataset[0]
            
            print(f"✓ 图像类型: {type(image)}")
            print(f"✓ 图像模式: {image.mode}")
            print(f"✓ 图像尺寸: {image.size}")
            print(f"✓ 标签: {label}")
            
            # 验证是否为RGB图像且尺寸正确
            assert image.mode == 'RGB', f"期望RGB模式，实际: {image.mode}"
            assert image.size == (224, 224), f"期望(224, 224)尺寸，实际: {image.size}"
            
            print("✓ 数据集输出格式测试通过")
            return True
        else:
            print("✗ 数据集为空")
            return False
            
    finally:
        shutil.rmtree(test_dir)


def test_transform_pipeline():
    """测试变换流程"""
    print("\n测试变换流程...")
    
    # 创建临时测试数据
    test_dir = tempfile.mkdtemp()
    try:
        # 创建测试文件
        test_file = os.path.join(test_dir, "train", "t2", "test.nii.gz")
        create_test_nii_file(test_file, shape=(80, 90, 70))  # 不规则尺寸
        
        # 创建带变换的数据集
        from torchvision import transforms
        
        transform = transforms.Compose([
            transforms.RandomHorizontalFlip(p=0),  # 不翻转，便于测试
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        dataset = MedicalImageDataset(
            data_dir=os.path.join(test_dir, "train"),
            slice_mode='middle',
            transform=transform
        )
        
        if len(dataset) > 0:
            # 获取样本
            tensor_image, label = dataset[0]
            
            print(f"✓ 张量形状: {tensor_image.shape}")
            print(f"✓ 张量数据类型: {tensor_image.dtype}")
            print(f"✓ 张量值范围: [{tensor_image.min():.3f}, {tensor_image.max():.3f}]")
            
            # 验证张量格式
            assert tensor_image.shape == (3, 224, 224), f"期望(3, 224, 224)形状，实际: {tensor_image.shape}"
            assert tensor_image.dtype == torch.float32, f"期望float32类型，实际: {tensor_image.dtype}"
            
            print("✓ 变换流程测试通过")
            return True
        else:
            print("✗ 数据集为空")
            return False
            
    finally:
        shutil.rmtree(test_dir)


def test_different_slice_modes():
    """测试不同切片模式"""
    print("\n测试不同切片模式...")
    
    # 创建临时测试数据
    test_dir = tempfile.mkdtemp()
    try:
        # 创建测试文件
        test_file = os.path.join(test_dir, "train", "flair", "test.nii.gz")
        create_test_nii_file(test_file)
        
        modes = [
            ('middle', 'z'),
            ('multi', 'z'),
            ('multi', 'y'),
            ('multi', 'x')
        ]
        
        for slice_mode, axis in modes:
            print(f"  测试模式: {slice_mode}, 轴向: {axis}")
            
            dataset = MedicalImageDataset(
                data_dir=os.path.join(test_dir, "train"),
                slice_mode=slice_mode,
                axis=axis
            )
            
            if len(dataset) > 0:
                image, label = dataset[0]
                
                # 验证输出格式
                assert image.mode == 'RGB', f"模式 {slice_mode}_{axis}: 期望RGB，实际 {image.mode}"
                assert image.size == (224, 224), f"模式 {slice_mode}_{axis}: 期望(224,224)，实际 {image.size}"
                
                print(f"    ✓ {slice_mode}_{axis} 模式测试通过")
            else:
                print(f"    ✗ {slice_mode}_{axis} 模式数据集为空")
        
        print("✓ 所有切片模式测试通过")
        return True
        
    finally:
        shutil.rmtree(test_dir)


def test_data_loader_integration():
    """测试数据加载器集成"""
    print("\n测试数据加载器集成...")
    
    # 创建临时测试数据
    test_dir = tempfile.mkdtemp()
    try:
        # 创建多个测试文件
        sequences = ['t1', 't2', 'flair', 'dwi']
        for seq in sequences:
            for i in range(2):  # 每个序列2个文件
                test_file = os.path.join(test_dir, "train", seq, f"test_{i}.nii.gz")
                create_test_nii_file(test_file)
        
        # 创建配置
        config = Config()
        original_data_dir = config.DATA_DIR
        config.DATA_DIR = test_dir
        config.BATCH_SIZE = 4
        
        try:
            # 创建数据加载器
            data_loaders, class_names, dataset_sizes = get_medical_data_loaders(
                config, slice_mode='middle'
            )
            
            print(f"✓ 类别名称: {class_names}")
            print(f"✓ 数据集大小: {dataset_sizes}")
            
            if 'train' in data_loaders:
                train_loader = data_loaders['train']
                
                # 测试一个批次
                for batch_idx, (images, labels) in enumerate(train_loader):
                    print(f"✓ 批次形状: {images.shape}")
                    print(f"✓ 标签形状: {labels.shape}")
                    print(f"✓ 图像数据类型: {images.dtype}")
                    print(f"✓ 图像值范围: [{images.min():.3f}, {images.max():.3f}]")
                    
                    # 验证批次格式
                    assert images.shape[1:] == (3, 224, 224), f"期望(*, 3, 224, 224)，实际: {images.shape}"
                    assert images.dtype == torch.float32, f"期望float32，实际: {images.dtype}"
                    
                    break  # 只测试第一个批次
                
                print("✓ 数据加载器集成测试通过")
                return True
            else:
                print("✗ 未找到训练数据加载器")
                return False
                
        finally:
            config.DATA_DIR = original_data_dir
            
    finally:
        shutil.rmtree(test_dir)


def test_memory_efficiency():
    """测试内存效率"""
    print("\n测试内存效率...")
    
    # 创建临时测试数据
    test_dir = tempfile.mkdtemp()
    try:
        # 创建较大的测试文件
        test_file = os.path.join(test_dir, "train", "t1", "large_test.nii.gz")
        create_test_nii_file(test_file, shape=(128, 128, 128))  # 较大尺寸
        
        # 测试多次访问
        dataset = MedicalImageDataset(
            data_dir=os.path.join(test_dir, "train"),
            slice_mode='middle'
        )
        
        if len(dataset) > 0:
            # 多次获取同一样本，测试是否有内存泄漏
            for i in range(10):
                image, label = dataset[0]
                assert image.size == (224, 224), f"第{i}次访问尺寸错误"
            
            print("✓ 内存效率测试通过")
            return True
        else:
            print("✗ 数据集为空")
            return False
            
    finally:
        shutil.rmtree(test_dir)


def main():
    """主测试函数"""
    print("=" * 60)
    print("优化后的医学影像数据加载器测试")
    print("=" * 60)
    
    tests = [
        test_dataset_output_format,
        test_transform_pipeline,
        test_different_slice_modes,
        test_data_loader_integration,
        test_memory_efficiency
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_func.__name__} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("✓ 所有测试通过！优化成功！")
        print("\n主要改进:")
        print("- 移除了冗余的PadToSquare变换")
        print("- 在切片堆叠阶段直接处理尺寸统一")
        print("- 简化了数据预处理流程")
        print("- 保持了224x224的标准输出尺寸")
        print("- 维持了3通道RGB图像格式")
    else:
        print("✗ 部分测试失败，需要检查实现")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
