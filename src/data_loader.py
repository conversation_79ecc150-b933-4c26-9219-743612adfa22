import os
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import datasets, transforms, models
import torch.nn.functional as F
from src.utils import  PadToSquare

class MedicalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None):
        """
        初始化数据集。
        Args:
            data_dir (str): 包含图像和标签的文件夹路径。
            transform (callable, optional): 应用于图像的变换。
        """
        self.data_dir = data_dir
        self.transform = transform
        self.image_paths = []
        self.labels = []

        # 假设数据目录结构为 data_dir/label1/image.png, data_dir/label2/image.png
        for label_name in os.listdir(data_dir):
            label_path = os.path.join(data_dir, label_name)
            if os.path.isdir(label_path):
                for image_name in os.listdir(label_path):
                    self.image_paths.append(os.path.join(label_path, image_name))
                    self.labels.append(int(label_name))  # 假设标签是整数

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        image = Image.open(image_path).convert("RGB")
        label = self.labels[idx]

        if self.transform:
            image = self.transform(image)

        return image, label


def get_data_loaders(config):
    """
    创建并返回训练、验证和测试数据加载器。
    """
    # 定义数据增强和预处理
    data_transforms = {
        "train": transforms.Compose([
            PadToSquare(config.IMAGE_SIZE),  # 步骤1: 统一尺寸，保持纵横比
            transforms.RandomHorizontalFlip(),  # 步骤2: 数据增强 - 水平翻转
            transforms.RandomRotation(10),  # 步骤2: 数据增强 - 随机旋转
            transforms.Grayscale(num_output_channels=3),  # 步骤3: 灰度转三通道
            transforms.ToTensor(),  # 步骤4: 转为Tensor (值在0-1之间)
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)  # 步骤5: 标准化
        ]),
        # 验证/测试集的预处理 (通常不做数据增强)
        "val": transforms.Compose([
            PadToSquare(config.IMAGE_SIZE),
            transforms.Grayscale(num_output_channels=3),
            transforms.ToTensor(),
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)
        ]),
        "test": transforms.Compose([
            PadToSquare(config.IMAGE_SIZE),
            transforms.Grayscale(num_output_channels=3),
            transforms.ToTensor(),
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)
        ])}

    image_datasets = {x: datasets.ImageFolder(root=f'{config.DATA_DIR}/{x}', transform=data_transforms[x])
                      for x in ['train', 'val', 'test']}

    data_loaders = {x: DataLoader(image_datasets[x], batch_size=config.BATCH_SIZE, shuffle=True, num_workers=config.NUM_WORKERS)
                   for x in ['train', 'val']}

    # 测试集的 shuffle 通常为 False
    data_loaders['test'] = DataLoader(image_datasets['test'], batch_size=config.BATCH_SIZE, shuffle=False, num_workers=config.NUM_WORKERS)

    dataset_sizes = {x: len(image_datasets[x]) for x in ['train', 'val', 'test']}
    class_names = image_datasets['train'].classes
    num_classes = len(class_names)

    print(f"训练集大小: {dataset_sizes['train']}")
    print(f"验证集大小: {dataset_sizes['val']}")
    print(f"测试集大小: {dataset_sizes['test']}")
    print(f"类别数量: {num_classes}")
    print(f"类别名称: {class_names}")

    return data_loaders,class_names, dataset_sizes