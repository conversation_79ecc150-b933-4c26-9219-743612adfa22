import os
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import datasets, transforms, models
import torch.nn.functional as F
import SimpleITK as sitk
import numpy as np
from PIL import Image
from src.utils import PadToSquare

class MedicalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None, slice_mode='middle', axis='z'):
        """
        初始化医学影像数据集。
        Args:
            data_dir (str): 包含nii.gz文件和标签的文件夹路径。
            transform (callable, optional): 应用于图像的变换。
            slice_mode (str): 切片模式，'middle' 或 'multi'
                - 'middle': 取xyz面的中间切片
                - 'multi': 取指定轴的25%、50%、75%位置切片
            axis (str): 当slice_mode='multi'时指定轴向，'x', 'y', 'z'，默认'z'
        """
        self.data_dir = data_dir
        self.transform = transform
        self.slice_mode = slice_mode
        self.axis = axis
        self.image_paths = []
        self.labels = []
        self.label_to_idx = {}

        # 收集所有nii.gz文件和对应标签
        self._load_dataset()

    def _load_dataset(self):
        """加载数据集，支持两种目录结构：
        1. data_dir/label_name/file.nii.gz
        2. data_dir/file_label.nii.gz (从文件名解析标签)
        """
        label_names = set()

        # 遍历数据目录
        for item in os.listdir(self.data_dir):
            item_path = os.path.join(self.data_dir, item)

            if os.path.isdir(item_path):
                # 目录结构：data_dir/label_name/file.nii.gz
                label_name = item
                label_names.add(label_name)

                for file_name in os.listdir(item_path):
                    if file_name.endswith('.nii.gz') or file_name.endswith('.nii'):
                        file_path = os.path.join(item_path, file_name)
                        self.image_paths.append(file_path)
                        self.labels.append(label_name)

            elif item.endswith('.nii.gz') or item.endswith('.nii'):
                # 文件结构：data_dir/file_label.nii.gz
                # 从文件名解析标签（假设格式为：xxx_label.nii.gz）
                file_name = os.path.splitext(os.path.splitext(item)[0])[0]  # 去掉.nii.gz
                parts = file_name.split('_')
                if len(parts) >= 2:
                    label_name = parts[-1]  # 取最后一部分作为标签
                    label_names.add(label_name)
                    self.image_paths.append(item_path)
                    self.labels.append(label_name)

        # 创建标签到索引的映射
        sorted_labels = sorted(list(label_names))
        self.label_to_idx = {label: idx for idx, label in enumerate(sorted_labels)}
        self.idx_to_label = {idx: label for label, idx in self.label_to_idx.items()}

        # 将标签转换为数字索引
        self.labels = [self.label_to_idx[label] for label in self.labels]

        print(f"找到 {len(self.image_paths)} 个医学影像文件")
        print(f"标签类别: {sorted_labels}")
        print(f"各类别数量: {dict(zip(sorted_labels, [self.labels.count(i) for i in range(len(sorted_labels))]))}")

    def _extract_slices(self, nii_path):
        """从nii.gz文件提取切片"""
        try:
            # 读取医学影像
            image = sitk.ReadImage(nii_path)
            arr = sitk.GetArrayFromImage(image)  # 形状通常是 (depth, height, width)

            if self.slice_mode == 'middle':
                return self._extract_middle_slices(arr)
            elif self.slice_mode == 'multi':
                return self._extract_multi_slices(arr)
            else:
                raise ValueError(f"不支持的切片模式: {self.slice_mode}")

        except Exception as e:
            print(f"读取文件 {nii_path} 时出错: {e}")
            # 返回默认的黑色图像
            return [Image.new('L', (224, 224), 0)]

    def _extract_middle_slices(self, arr):
        """提取xyz面的中间切片"""
        depth, height, width = arr.shape
        slices = []

        # Z轴中间切片 (轴向面)
        z_middle = depth // 2
        axial_slice = arr[z_middle, :, :]
        slices.append(self._array_to_pil(axial_slice))

        # Y轴中间切片 (冠状面)
        y_middle = height // 2
        coronal_slice = arr[:, y_middle, :]
        slices.append(self._array_to_pil(coronal_slice))

        # X轴中间切片 (矢状面)
        x_middle = width // 2
        sagittal_slice = arr[:, :, x_middle]
        slices.append(self._array_to_pil(sagittal_slice))

        return slices

    def _extract_multi_slices(self, arr):
        """提取指定轴的25%、50%、75%位置切片"""
        depth, height, width = arr.shape
        slices = []

        if self.axis == 'z':
            positions = [int(depth * 0.25), int(depth * 0.5), int(depth * 0.75)]
            for pos in positions:
                pos = min(pos, depth - 1)  # 确保不超出边界
                slice_data = arr[pos, :, :]
                slices.append(self._array_to_pil(slice_data))

        elif self.axis == 'y':
            positions = [int(height * 0.25), int(height * 0.5), int(height * 0.75)]
            for pos in positions:
                pos = min(pos, height - 1)
                slice_data = arr[:, pos, :]
                slices.append(self._array_to_pil(slice_data))

        elif self.axis == 'x':
            positions = [int(width * 0.25), int(width * 0.5), int(width * 0.75)]
            for pos in positions:
                pos = min(pos, width - 1)
                slice_data = arr[:, :, pos]
                slices.append(self._array_to_pil(slice_data))
        else:
            raise ValueError(f"不支持的轴向: {self.axis}")

        return slices

    def _array_to_pil(self, arr):
        """将numpy数组转换为PIL图像"""
        # 标准化到0-255范围
        arr_normalized = ((arr - arr.min()) / (arr.max() - arr.min() + 1e-8) * 255).astype(np.uint8)
        return Image.fromarray(arr_normalized, mode='L')

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        nii_path = self.image_paths[idx]
        label = self.labels[idx]

        # 提取切片
        slices = self._extract_slices(nii_path)

        # 如果是多切片模式，可以选择合并或返回第一个切片
        # 这里默认返回第一个切片，您可以根据需要修改
        image = slices[0]

        if self.transform:
            image = self.transform(image)

        return image, label

    def get_class_names(self):
        """返回类别名称列表"""
        return [self.idx_to_label[i] for i in range(len(self.label_to_idx))]


def get_data_loaders(config, slice_mode='middle', axis='z', use_medical_dataset=True):
    """
    创建并返回训练、验证和测试数据加载器。

    Args:
        config: 配置对象
        slice_mode (str): 切片模式，'middle' 或 'multi'
        axis (str): 当slice_mode='multi'时指定轴向，'x', 'y', 'z'
        use_medical_dataset (bool): 是否使用医学影像数据集
    """
    # 定义数据增强和预处理
    data_transforms = {
        "train": transforms.Compose([
            PadToSquare(config.IMAGE_SIZE),  # 步骤1: 统一尺寸，保持纵横比
            transforms.RandomHorizontalFlip(),  # 步骤2: 数据增强 - 水平翻转
            transforms.RandomRotation(10),  # 步骤2: 数据增强 - 随机旋转
            transforms.Lambda(lambda x: x.convert('RGB') if x.mode != 'RGB' else x),  # 转换为RGB
            transforms.ToTensor(),  # 步骤4: 转为Tensor (值在0-1之间)
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)  # 步骤5: 标准化
        ]),
        # 验证/测试集的预处理 (通常不做数据增强)
        "val": transforms.Compose([
            PadToSquare(config.IMAGE_SIZE),
            transforms.Lambda(lambda x: x.convert('RGB') if x.mode != 'RGB' else x),
            transforms.ToTensor(),
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)
        ]),
        "test": transforms.Compose([
            PadToSquare(config.IMAGE_SIZE),
            transforms.Lambda(lambda x: x.convert('RGB') if x.mode != 'RGB' else x),
            transforms.ToTensor(),
            transforms.Normalize(mean=config.IMAGENET_MEAN, std=config.IMAGENET_STD)
        ])}

    if use_medical_dataset:
        # 使用医学影像数据集
        image_datasets = {}
        for split in ['train', 'val', 'test']:
            split_dir = os.path.join(config.DATA_DIR, split)
            if os.path.exists(split_dir):
                image_datasets[split] = MedicalImageDataset(
                    data_dir=split_dir,
                    transform=data_transforms[split],
                    slice_mode=slice_mode,
                    axis=axis
                )
            else:
                print(f"警告: 目录 {split_dir} 不存在，跳过 {split} 数据集")
    else:
        # 使用传统的ImageFolder数据集
        image_datasets = {x: datasets.ImageFolder(root=f'{config.DATA_DIR}/{x}', transform=data_transforms[x])
                          for x in ['train', 'val', 'test'] if os.path.exists(f'{config.DATA_DIR}/{x}')}

    # 创建数据加载器
    data_loaders = {}
    for split in ['train', 'val']:
        if split in image_datasets:
            shuffle = (split == 'train')  # 只有训练集需要shuffle
            data_loaders[split] = DataLoader(
                image_datasets[split],
                batch_size=config.BATCH_SIZE,
                shuffle=shuffle,
                num_workers=config.NUM_WORKERS
            )

    # 测试集的 shuffle 通常为 False
    if 'test' in image_datasets:
        data_loaders['test'] = DataLoader(
            image_datasets['test'],
            batch_size=config.BATCH_SIZE,
            shuffle=False,
            num_workers=config.NUM_WORKERS
        )

    dataset_sizes = {x: len(image_datasets[x]) for x in image_datasets.keys()}

    # 获取类别名称
    if use_medical_dataset and 'train' in image_datasets:
        class_names = image_datasets['train'].get_class_names()
    elif not use_medical_dataset and 'train' in image_datasets:
        class_names = image_datasets['train'].classes
    else:
        class_names = []

    num_classes = len(class_names)

    print(f"数据集信息:")
    for split, size in dataset_sizes.items():
        print(f"  {split}集大小: {size}")
    print(f"类别数量: {num_classes}")
    print(f"类别名称: {class_names}")
    print(f"切片模式: {slice_mode}")
    if slice_mode == 'multi':
        print(f"切片轴向: {axis}")

    return data_loaders, class_names, dataset_sizes


def get_medical_data_loaders(config, slice_mode='middle', axis='z'):
    """
    专门用于医学影像的数据加载器创建函数

    Args:
        config: 配置对象
        slice_mode (str): 切片模式
            - 'middle': 取xyz面的中间切片
            - 'multi': 取指定轴的25%、50%、75%位置切片
        axis (str): 当slice_mode='multi'时指定轴向，'x', 'y', 'z'，默认'z'
    """
    return get_data_loaders(config, slice_mode=slice_mode, axis=axis, use_medical_dataset=True)