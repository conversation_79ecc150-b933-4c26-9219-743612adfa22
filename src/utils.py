import os
import torch

import <PERSON>IT<PERSON> as sitk
from PIL import Image
import numpy as np
import hashlib

def get_md5_hash(file_path):
    """
    Calculates the MD5 hash of a file.

    Args:
        file_path (str): The path to the input file.

    Returns:
        str: The MD5 hash of the file as a hexadecimal string,
             or an error message if the file cannot be read.
    """
    # Initialize the MD5 hash object
    md5_hash = hashlib.md5()

    try:
        # Open the file in binary read mode to ensure consistent hashing across platforms
        with open(file_path, "rb") as f:
            # Read the file in chunks to handle large files efficiently
            for chunk in iter(lambda: f.read(4096), b""):
                # Update the hash object with each chunk
                md5_hash.update(chunk)
    except FileNotFoundError:
        return f"Error: The file '{file_path}' was not found."
    except IOError:
        return f"Error: Could not read the file '{file_path}'."

    # Return the hexadecimal representation of the hash
    return md5_hash.hexdigest()


def image2d_to_obj(arr, resize_rect=None):
    rgb_image = Image.fromarray(arr)
    if resize_rect:
        rgb_image = rgb_image.resize(resize_rect, Image.LANCZOS)
    return rgb_image


def ct_arr_to_gray(arr, window_width=400, window_center=40):
    return np.uint8(np.clip(255 * ((arr - window_center) / (window_width) + 1 / 2), 0, 255))


def cut_nii_to_image_obj(nii_path):
    image = sitk.ReadImage(nii_path)
    arr = sitk.GetArrayFromImage(image)
    depth, height, width = arr.shape
    spacing_width, spacing_height, spacing_depth = image.GetSpacing()
    arr = arr[::-1, :, :]
    arr1 = arr[:, int(height / 2), :]
    arr2 = arr[:, :, int(width / 2)]

    resize_rect = (width, int(depth * spacing_depth / spacing_width))
    cor_img_obj = image2d_to_obj(ct_arr_to_gray(arr1), resize_rect=resize_rect)

    resize_rect = (height, int(depth * spacing_depth / spacing_height))
    sag_img_obj = image2d_to_obj(ct_arr_to_gray(arr2), resize_rect=resize_rect)
    return cor_img_obj, sag_img_obj



def save_checkpoint(model, optimizer, epoch, accuracy, class_names, path, model_name):
    """
    保存模型检查点。

    Args:
        model (torch.nn.Module): 要保存的模型。
        optimizer (torch.optim.Optimizer): 优化器状态。
        epoch (int): 当前训练的周期数。
        accuracy (float): 模型在验证集上的准确率。
        class_names (list): 类别列表
        path (str): 检查点保存的目录。
        model_name (str): model_name
    """
    if not os.path.exists(path):
        os.makedirs(path)

    # 确保文件名是唯一的，并包含关键信息
    checkpoint_path = os.path.join(path, model_name)

    print(f"正在保存模型检查点到 {checkpoint_path} (准确率: {accuracy:.4f})")
    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'accuracy': accuracy,
        'class_names': class_names
    }, checkpoint_path)


# 自定义一个Transform来实现“保持纵横比缩放+填充”
class PadToSquare:
    def __init__(self, size, fill=0):
        self.size = size
        self.fill = fill

    def __call__(self, img):
        # img: a PIL Image
        w, h = img.size
        # 计算缩放后的尺寸
        if w > h:
            new_w = self.size
            new_h = int(h * self.size / w)
        else:
            new_h = self.size
            new_w = int(w * self.size / h)

        resized_img = img.resize((new_w, new_h), Image.BICUBIC)

        # 创建一个正方形的背景
        new_img = Image.new("L", (self.size, self.size), self.fill)  # "L" for grayscale
        # 将缩放后的图片粘贴到中心
        paste_x = (self.size - new_w) // 2
        paste_y = (self.size - new_h) // 2
        new_img.paste(resized_img, (paste_x, paste_y))

        return new_img
